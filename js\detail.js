document.addEventListener('DOMContentLoaded', function() {
    // 放大镜效果
    const zoomImage = document.getElementById('zoom-image');
    const zoomResult = document.getElementById('zoom-result');
    
    if (zoomImage && zoomResult) {
        zoomImage.addEventListener('mousemove', function(e) {
            // 计算鼠标在图片中的位置
            const rect = zoomImage.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 计算放大镜显示的位置和背景位置
            const resultWidth = zoomResult.offsetWidth;
            const resultHeight = zoomResult.offsetHeight;
            
            const imgWidth = zoomImage.offsetWidth;
            const imgHeight = zoomImage.offsetHeight;
            
            // 限制放大镜不超出图片范围
            let posX = x - resultWidth / 2;
            let posY = y - resultHeight / 2;
            
            if (posX < 0) posX = 0;
            if (posY < 0) posY = 0;
            if (posX > imgWidth - resultWidth) posX = imgWidth - resultWidth;
            if (posY > imgHeight - resultHeight) posY = imgHeight - resultHeight;
            
            // 计算背景位置 (放大2倍)
            const bgPosX = -posX * 2;
            const bgPosY = -posY * 2;
            
            // 更新放大镜位置和背景
            zoomResult.style.left = posX + 'px';
            zoomResult.style.top = posY + 'px';
            zoomResult.style.backgroundImage = `url('${zoomImage.getAttribute('data-zoom-image')}')`;
            zoomResult.style.backgroundPosition = `${bgPosX}px ${bgPosY}px`;
            zoomResult.style.display = 'block';
        });
        
        zoomImage.addEventListener('mouseleave', function() {
            zoomResult.style.display = 'none';
        });
    }
    
    // 缩略图切换
    const thumbnails = document.querySelectorAll('.thumbnail');
    const mainImage = document.getElementById('zoom-image');
    
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            // 更新激活状态
            thumbnails.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // 更新主图
            const imgSrc = this.querySelector('img').getAttribute('src').replace('-thumb', '-large');
            mainImage.setAttribute('src', imgSrc);
            
            // 更新放大镜的大图
            const zoomSrc = imgSrc.replace('-large', '-zoom');
            mainImage.setAttribute('data-zoom-image', zoomSrc);
        });
    });
    
    // 数量增减
    const decreaseBtn = document.querySelector('.quantity-decrease');
    const increaseBtn = document.querySelector('.quantity-increase');
    const quantityInput = document.querySelector('.quantity-input');
    
    if (decreaseBtn && increaseBtn && quantityInput) {
        decreaseBtn.addEventListener('click', function() {
            let value = parseInt(quantityInput.value);
            if (value > 1) {
                quantityInput.value = value - 1;
            }
        });
        
        increaseBtn.addEventListener('click', function() {
            let value = parseInt(quantityInput.value);
            if (value < 99) {
                quantityInput.value = value + 1;
            }
        });
        
        quantityInput.addEventListener('change', function() {
            let value = parseInt(this.value);
            if (isNaN(value) {
                this.value = 1;
            } else if (value < 1) {
                this.value = 1;
            } else if (value > 99) {
                this.value = 99;
            }
        });
    }
    
    // 加入购物车按钮
    const addToCartBtn = document.querySelector('.add-to-cart-btn');
    if (addToCartBtn) {
        addToCartBtn.addEventListener('click', function() {
            // 获取商品ID (这里假设从URL获取)
            const urlParams = new URLSearchParams(window.location.search);
            const productId = urlParams.get('id') || '1';
            
            // 获取数量
            const quantity = parseInt(quantityInput.value) || 1;
            
            // 添加到购物车
            let cart = JSON.parse(localStorage.getItem('cart')) || [];
            const existingItem = cart.find(item => item.id === productId);
            
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart