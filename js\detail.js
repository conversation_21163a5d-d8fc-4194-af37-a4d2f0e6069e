document.addEventListener('DOMContentLoaded', function() {
    // 初始化淘宝风格放大镜
    const mainImageContainer = document.querySelector('.main-image.taobao-magnifier-container');
    if (mainImageContainer) {
        // 创建淘宝风格放大镜实例
        const taobaoMagnifier = new TaobaoMagnifier(mainImageContainer, {
            lensWidth: 200,
            lensHeight: 200,
            resultWidth: 400,
            resultHeight: 400,
            zoomFactor: 3,
            position: 'right',
            gap: 30,
            hintText: '移动鼠标查看放大效果',
            enableAnimation: true
        });

        // 将放大镜实例保存到全局，方便其他地方使用
        window.detailTaobaoMagnifier = taobaoMagnifier;
    }

    // 缩略图切换
    const thumbnails = document.querySelectorAll('.thumbnail');
    const mainImage = document.getElementById('zoom-image');

    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            // 更新激活状态
            thumbnails.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // 更新主图
            const imgSrc = this.querySelector('img').getAttribute('src');
            mainImage.setAttribute('src', imgSrc);
            mainImage.setAttribute('data-zoom-image', imgSrc);

            // 更新淘宝放大镜的图片
            if (window.detailTaobaoMagnifier) {
                window.detailTaobaoMagnifier.updateImage(imgSrc, imgSrc);
            }
        });
    });

    // 数量增减
    const decreaseBtn = document.querySelector('.quantity-decrease');
    const increaseBtn = document.querySelector('.quantity-increase');
    const quantityInput = document.querySelector('.quantity-input');

    if (decreaseBtn && increaseBtn && quantityInput) {
        decreaseBtn.addEventListener('click', function() {
            let value = parseInt(quantityInput.value);
            if (value > 1) {
                quantityInput.value = value - 1;
            }
        });

        increaseBtn.addEventListener('click', function() {
            let value = parseInt(quantityInput.value);
            if (value < 99) {
                quantityInput.value = value + 1;
            }
        });

        quantityInput.addEventListener('change', function() {
            let value = parseInt(this.value);
            if (isNaN(value)) {
                this.value = 1;
            } else if (value < 1) {
                this.value = 1;
            } else if (value > 99) {
                this.value = 99;
            }
        });
    }

    // 加入购物车按钮
    const addToCartBtn = document.querySelector('.add-to-cart-btn');
    if (addToCartBtn) {
        addToCartBtn.addEventListener('click', function() {
            // 获取商品ID (这里假设从URL获取)
            const urlParams = new URLSearchParams(window.location.search);
            const productId = urlParams.get('id') || '1';

            // 获取数量
            const quantity = parseInt(quantityInput.value) || 1;

            // 添加到购物车
            let cart = JSON.parse(localStorage.getItem('cart')) || [];
            const existingItem = cart.find(item => item.id === productId);

            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({
                    id: productId,
                    quantity: quantity,
                    name: document.querySelector('.product-title').textContent,
                    price: document.querySelector('.current-price').textContent,
                    image: document.getElementById('zoom-image').src
                });
            }

            localStorage.setItem('cart', JSON.stringify(cart));

            // 更新购物车数量显示
            updateCartCount();

            alert('商品已添加到购物车！');
        });
    }
});

// 更新购物车数量
function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const totalItems = cart.reduce((total, item) => total + item.quantity, 0);

    const cartCountElement = document.getElementById('cart-count');
    if (cartCountElement) {
        cartCountElement.textContent = totalItems;
    }
}