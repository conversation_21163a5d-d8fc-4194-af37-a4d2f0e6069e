 <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
        }

        /* 顶部导航样式 */
        header {
            background-color: #f4f4f4;
            padding: 20px;
            text-align: center;
        }

        .logo {
            font-size: 24px;
            margin-bottom: 10px;
        }

        nav ul {
            list-style-type: none;
            display: flex;
            justify-content: center;
        }

        nav ul li {
            margin: 0 15px;
        }

        nav ul li a {
            text-decoration: none;
            color: #333;
        }

        /* 主内容区样式 */
        .main-content {
            display: flex;
            padding: 20px;
        }

        /* 侧边分类栏样式 */
        .sidebar {
            width: 200px;
            margin-right: 20px;
        }

        .sidebar ul {
            list-style-type: none;
        }

        .sidebar ul li {
            padding: 10px 0;
        }

        .sidebar ul li a {
            text-decoration: none;
            color: #333;
        }

        /* 商品展示区样式 */
        .product-list {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            grid-gap: 20px;
        }

        .product-item {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }

        .product-item img {
            width: 100%;
            height: auto;
            margin-bottom: 10px;
        }

        .product-item h3 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .product-item p {
            font-size: 14px;
            color: #666;
        }
    </style>