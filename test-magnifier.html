<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>放大镜功能测试</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/magnifier.css">
    <style>
        .test-container {
            padding: 50px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 50px;
            padding: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            margin-bottom: 20px;
            color: #333;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        .test-item {
            text-align: center;
        }
        .test-item h3 {
            margin-bottom: 15px;
            font-size: 16px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>放大镜功能测试页面</h1>
        
        <!-- 产品卡片测试 -->
        <div class="test-section">
            <h2>产品卡片放大镜测试</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>小尺寸放大镜</h3>
                    <div class="product-card">
                        <div class="product-image magnifier-container magnifier-small">
                            <img src="img/sp.jpg" alt="薯片">
                            <div class="magnifier-hint">悬停放大</div>
                        </div>
                    </div>
                </div>
                
                <div class="test-item">
                    <h3>中等尺寸放大镜</h3>
                    <div class="product-card">
                        <div class="product-image magnifier-container magnifier-medium">
                            <img src="img/qkl.jpg" alt="巧克力">
                            <div class="magnifier-hint">悬停放大</div>
                        </div>
                    </div>
                </div>
                
                <div class="test-item">
                    <h3>大尺寸放大镜</h3>
                    <div class="product-card">
                        <div class="product-image magnifier-container magnifier-large">
                            <img src="img/jg.png" alt="坚果">
                            <div class="magnifier-hint">悬停放大</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 详情页测试 -->
        <div class="test-section">
            <h2>详情页放大镜测试</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>详情页主图放大镜</h3>
                    <div class="main-image magnifier-container magnifier-large">
                        <img src="img/ls1.png" alt="薯片详情">
                        <div class="magnifier-hint">悬停放大查看细节</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 轮播图测试 -->
        <div class="test-section">
            <h2>轮播图放大镜测试</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>轮播图放大镜</h3>
                    <div class="slide magnifier-container">
                        <img src="img/lscx.png" alt="促销图">
                        <div class="magnifier-hint">悬停放大</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 自定义配置测试 -->
        <div class="test-section">
            <h2>自定义配置测试</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>高倍放大镜 (5倍)</h3>
                    <div class="magnifier-container" id="custom-magnifier-1">
                        <img src="img/nrg1.png" alt="牛肉干">
                        <div class="magnifier-hint">5倍放大</div>
                    </div>
                </div>
                
                <div class="test-item">
                    <h3>左侧显示放大镜</h3>
                    <div class="magnifier-container" id="custom-magnifier-2">
                        <img src="img/jxbg.png" alt="饼干">
                        <div class="magnifier-hint">左侧显示</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/magnifier.js"></script>
    <script>
        // 自定义配置的放大镜
        document.addEventListener('DOMContentLoaded', function() {
            // 高倍放大镜
            const customMagnifier1 = document.getElementById('custom-magnifier-1');
            if (customMagnifier1) {
                new Magnifier(customMagnifier1, {
                    zoomFactor: 5,
                    lensSize: 100,
                    resultSize: 300,
                    position: 'right',
                    hintText: '5倍放大'
                });
            }
            
            // 左侧显示放大镜
            const customMagnifier2 = document.getElementById('custom-magnifier-2');
            if (customMagnifier2) {
                new Magnifier(customMagnifier2, {
                    zoomFactor: 3,
                    lensSize: 120,
                    resultSize: 250,
                    position: 'left',
                    hintText: '左侧显示'
                });
            }
        });
    </script>
</body>
</html>
