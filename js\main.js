// 轮播图功能
document.addEventListener('DOMContentLoaded', function() {
    // 轮播图
    const slider = document.querySelector('.slider-wrapper');
    const slides = document.querySelectorAll('.slide');
    const dots = document.querySelectorAll('.dot');
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');

    let currentIndex = 0;
    const slideCount = slides.length;
    let slideInterval;

    // 初始化轮播图
    function initSlider() {
        updateSlider();
        startSlideShow();

        // 鼠标悬停暂停轮播
        const sliderContainer = document.querySelector('.slider-container');
        sliderContainer.addEventListener('mouseenter', pauseSlideShow);
        sliderContainer.addEventListener('mouseleave', startSlideShow);
    }

    // 更新轮播图位置
    function updateSlider() {
        slider.style.transform = `translateX(-${currentIndex * 100}%)`;

        // 更新指示点状态
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentIndex);
        });
    }

    // 下一张
    function nextSlide() {
        currentIndex = (currentIndex + 1) % slideCount;
        updateSlider();
    }

    // 上一张
    function prevSlide() {
        currentIndex = (currentIndex - 1 + slideCount) % slideCount;
        updateSlider();
    }

    // 开始自动轮播
    function startSlideShow() {
        slideInterval = setInterval(nextSlide, 3000);
    }

    // 暂停自动轮播
    function pauseSlideShow() {
        clearInterval(slideInterval);
    }

    // 按钮事件
    nextBtn.addEventListener('click', () => {
        pauseSlideShow();
        nextSlide();
    });

    prevBtn.addEventListener('click', () => {
        pauseSlideShow();
        prevSlide();
    });

    // 指示点点击事件
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            pauseSlideShow();
            currentIndex = index;
            updateSlider();
        });
    });

    // 初始化轮播图
    if (slider) {
        initSlider();
    }

    // 购物车数量更新
    updateCartCount();

    // 初始化淘宝风格放大镜
    initTaobaoMagnifiersForHomePage();

    // 快速查看和加入购物车按钮事件
    document.querySelectorAll('.quick-view').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');
            window.location.href = `detail.html?id=${productId}`;
        });
    });

    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');
            addToCart(productId);
        });
    });
});

// 购物车功能
function addToCart(productId) {
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({ id: productId, quantity: 1 });
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    alert('商品已添加到购物车');
}

function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const totalItems = cart.reduce((total, item) => total + item.quantity, 0);

    const cartCountElements = document.querySelectorAll('#cart-count');
    cartCountElements.forEach(element => {
        element.textContent = totalItems;
    });
}

// 首页专用的淘宝放大镜初始化
function initTaobaoMagnifiersForHomePage() {
    // 为产品卡片添加淘宝风格放大镜
    document.querySelectorAll('.product-card .product-image').forEach(element => {
        new TaobaoMagnifier(element, {
            lensWidth: 100,
            lensHeight: 100,
            resultWidth: 300,
            resultHeight: 300,
            zoomFactor: 2.5,
            position: 'right',
            gap: 20,
            hintText: '悬停查看放大效果'
        });
    });

    // 为轮播图添加淘宝风格放大镜
    document.querySelectorAll('.slide').forEach(element => {
        new TaobaoMagnifier(element, {
            lensWidth: 150,
            lensHeight: 150,
            resultWidth: 400,
            resultHeight: 400,
            zoomFactor: 2,
            position: 'right',
            gap: 30,
            hintText: '悬停查看放大效果'
        });
    });
}