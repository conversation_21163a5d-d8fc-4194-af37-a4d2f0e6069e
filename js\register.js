document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('register-form');
    
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('reg-username').value;
            const password = document.getElementById('reg-password').value;
            const confirmPassword = document.getElementById('reg-confirm-password').value;
            let isValid = true;
            
            // 验证用户名
            const usernameError = document.getElementById('username-error');
            if (!/^[a-zA-Z]+$/.test(username)) {
                usernameError.textContent = '用户名只能包含字母';
                usernameError.style.display = 'block';
                document.getElementById('reg-username').focus();
                isValid = false;
            } else {
                usernameError.style.display = 'none';
            }
            
            // 验证密码
            const passwordError = document.getElementById('password-error');
            if (!/^[a-zA-Z0-9_]+$/.test(password)) {
                passwordError.textContent = '密码只能包含字母、数字和下划线';
                passwordError.style.display = 'block';
                
                if (isValid) {
                    document.getElementById('reg-password').focus();
                }
                
                isValid = false;
            } else {
                passwordError.style.display = 'none';
            }
            
            // 验证确认密码
            const confirmPasswordError = document.getElementById('confirm-password-error');
            if (password !== confirmPassword) {
                confirmPasswordError.textContent = '两次输入的密码不一致';
                confirmPasswordError.style.display = 'block';
                
                if (isValid) {
                    document.getElementById('reg-confirm-password').focus();
                }
                
                isValid = false;
            } else {
                confirmPasswordError.style.display = 'none';
            }
            
            // 如果验证通过，提交表单
            if (isValid) {
                alert('注册成功！');
                // 实际项目中这里应该是AJAX请求
                window.location.href = 'login.html';
            }
        });
        
        // 重置按钮
        const resetBtn = registerForm.querySelector('.reset-btn');
        resetBtn.addEventListener('click', function() {
            document.querySelectorAll('.error-message').forEach(el => {
                el.style.display = 'none';
            });
        });
    }
});