/* 淘宝风格放大镜样式 */

/* 容器基础样式 */
.taobao-magnifier-container {
    position: relative;
    display: inline-block;
    overflow: hidden;
    cursor: crosshair;
}

.taobao-magnifier-container img {
    display: block;
    width: 100%;
    height: 100%;
    transition: opacity 0.3s ease;
}

/* 激活状态 */
.taobao-magnifier-active {
    user-select: none;
}

.taobao-magnifier-active img {
    opacity: 0.9;
}

/* 方形镜头样式 */
.taobao-magnifier-lens {
    position: absolute;
    border: 2px solid #ff6700;
    background: rgba(255, 255, 255, 0.3);
    cursor: move;
    display: none;
    z-index: 10;
    pointer-events: none;
    box-shadow: 
        0 0 10px rgba(0, 0, 0, 0.3),
        inset 0 0 20px rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(1px);
    transition: all 0.1s ease-out;
}

/* 放大结果区域 */
.taobao-magnifier-result {
    position: absolute;
    border: 2px solid #ff6700;
    background: white;
    display: none;
    z-index: 1000;
    overflow: hidden;
    box-shadow: 
        0 8px 30px rgba(0, 0, 0, 0.15),
        0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.taobao-magnifier-result::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff6700, #ff8533, #ff6700);
    border-radius: 6px;
    z-index: -1;
    opacity: 0.8;
}

/* 放大图片样式 */
.taobao-magnifier-result img {
    position: absolute;
    max-width: none;
    max-height: none;
    transition: all 0.1s ease-out;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* 提示文字样式 */
.taobao-magnifier-hint {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 15;
    pointer-events: none;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.taobao-magnifier-container:hover .taobao-magnifier-hint {
    opacity: 1;
    transform: translateY(-2px);
}

/* 加载状态 */
.taobao-magnifier-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 103, 0, 0.3);
    border-top: 3px solid #ff6700;
    border-radius: 50%;
    animation: taobao-magnifier-spin 1s linear infinite;
    z-index: 20;
}

@keyframes taobao-magnifier-spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 不同尺寸的淘宝放大镜 */
.taobao-magnifier-small .taobao-magnifier-lens {
    width: 80px;
    height: 80px;
}

.taobao-magnifier-medium .taobao-magnifier-lens {
    width: 120px;
    height: 120px;
}

.taobao-magnifier-large .taobao-magnifier-lens {
    width: 200px;
    height: 200px;
}

/* 产品详情页专用样式 */
.product-detail .taobao-magnifier-container {
    width: 100%;
    height: 100%;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.product-detail .taobao-magnifier-result {
    border-radius: 8px;
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.15),
        0 6px 20px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 产品卡片中的淘宝放大镜 */
.product-card .taobao-magnifier-container {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    overflow: hidden;
}

.product-card .taobao-magnifier-lens {
    border-width: 1px;
}

.product-card .taobao-magnifier-result {
    border-width: 1px;
    border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .taobao-magnifier-result {
        width: 300px !important;
        height: 300px !important;
    }
}

@media (max-width: 768px) {
    .taobao-magnifier-lens,
    .taobao-magnifier-result {
        display: none !important;
    }
    
    .taobao-magnifier-container {
        cursor: default !important;
    }
    
    .taobao-magnifier-hint {
        display: none !important;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .taobao-magnifier-result img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .taobao-magnifier-result {
        background: #2a2a2a;
        border-color: #ff8533;
    }
    
    .taobao-magnifier-lens {
        border-color: #ff8533;
        background: rgba(255, 255, 255, 0.1);
    }
    
    .taobao-magnifier-hint {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
        color: #333;
        border-color: rgba(0, 0, 0, 0.1);
    }
}

/* 动画效果增强 */
.taobao-magnifier-container {
    transition: transform 0.2s ease;
}

.taobao-magnifier-container:hover {
    transform: scale(1.02);
}

.taobao-magnifier-result {
    animation: taobao-magnifier-fadeIn 0.3s ease-out;
}

@keyframes taobao-magnifier-fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 镜头边框动画 */
.taobao-magnifier-lens {
    animation: taobao-magnifier-pulse 2s infinite;
}

@keyframes taobao-magnifier-pulse {
    0%, 100% {
        box-shadow: 
            0 0 10px rgba(0, 0, 0, 0.3),
            inset 0 0 20px rgba(255, 255, 255, 0.2),
            0 0 0 0 rgba(255, 103, 0, 0.4);
    }
    50% {
        box-shadow: 
            0 0 15px rgba(0, 0, 0, 0.4),
            inset 0 0 25px rgba(255, 255, 255, 0.3),
            0 0 0 4px rgba(255, 103, 0, 0.2);
    }
}

/* 禁用选择和拖拽 */
.taobao-magnifier-container,
.taobao-magnifier-container * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}
