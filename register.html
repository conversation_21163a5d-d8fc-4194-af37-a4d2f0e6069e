<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美味零食商城 - 注册</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/register.css">
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="index.html">美味零食商城</a></h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li><a href="lsfl.html">零食分类</a></li>
                    <li><a href="xpss.html">新品上市</a></li>
                    <li><a href="cxhd.html">促销活动</a></li>
                </ul>
            </nav>
            <div class="user-actions">
                <a href="login.html" class="login-btn">登录</a>
                <a href="register.html" class="register-btn active">注册</a>
                <a href="cart.html" class="cart-btn">购物车(<span id="cart-count">0</span>)</a>
            </div>
        </div>
    </header>
    <!-- 注册表单 -->
    <section class="register-section">
        <div class="container">
            <div class="register-form-container">
                <h2>用户注册</h2>
                <form id="register-form" class="register-form">
                    <div class="form-group">
                        <label for="reg-username">用户名</label>
                        <input type="text" id="reg-username" name="username" placeholder="请输入用户名(字母组成)" required>
                        <span class="error-message" id="username-error"></span>
                    </div>
                    <div class="form-group">
                        <label for="reg-password">密码</label>
                        <input type="password" id="reg-password" name="password" placeholder="请输入密码(包含字母、数字、下划线)" required>
                        <span class="error-message" id="password-error"></span>
                    </div>
                    <div class="form-group">
                        <label for="reg-confirm-password">确认密码</label>
                        <input type="password" id="reg-confirm-password" name="confirm-password" placeholder="请再次输入密码" required>
                        <span class="error-message" id="confirm-password-error"></span>
                    </div>
                    <div class="form-group">
                        <label>性别</label>
                        <div class="radio-group">
                            <label><input type="radio" name="gender" value="male" checked> 男</label>
                            <label><input type="radio" name="gender" value="female"> 女</label>
                            <label><input type="radio" name="gender" value="other"> 其他</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="birthday">生日</label>
                        <input type="date" id="birthday" name="birthday" required>
                    </div>
                    <div class="form-group">
                        <label>兴趣爱好</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" name="hobby" value="sweet"> 甜食</label>
                            <label><input type="checkbox" name="hobby" value="spicy"> 辣味</label>
                            <label><input type="checkbox" name="hobby" value="nut"> 坚果</label>
                            <label><input type="checkbox" name="hobby" value="fruit"> 果脯</label>
                            <label><input type="checkbox" name="hobby" value="imported"> 进口零食</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="email">电子邮箱</label>
                        <input type="email" id="email" name="email" placeholder="请输入电子邮箱" required>
                        <span class="error-message" id="email-error"></span>
                    </div>
                    <div class="form-group">
                        <label for="phone">手机号码</label>
                        <input type="tel" id="phone" name="phone" placeholder="请输入手机号码" required>
                        <span class="error-message" id="phone-error"></span>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn register-btn">注册</button>
                        <button type="reset" class="btn reset-btn">重置</button>
                    </div>
                    <div class="form-footer">
                        <p>已有账号？<a href="login.html">立即登录</a></p>
                    </div>
                </form>
            </div>
        </div>
    </section>
    <!-- 底部信息 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>关于我们</h3>
                    <p>美味零食商城致力于为您提供最优质、最健康的零食选择。</p>
                </div>
                <div class="footer-section">
                    <h3>联系我们</h3>
                    <p>电话: ************</p>
                    <p>邮箱: <EMAIL></p>
                </div>
                <div class="footer-section">
                    <h3>快速链接</h3>
                    <ul>
                        <li><a href="#">帮助中心</a></li>
                        <li><a href="#">配送方式</a></li>
                        <li><a href="#">支付方式</a></li>
                        <li><a href="#">售后服务</a></li>
                    </ul>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 美味零食商城 版权所有</p>
            </div>
        </div>
    </footer>
    <script src="js/register.js"></script>
</body>
</html>