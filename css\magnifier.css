/* 通用放大镜样式 */
.magnifier-container {
    position: relative;
    display: inline-block;
    overflow: hidden;
}

.magnifier-container img {
    display: block;
    width: 100%;
    height: 100%;
    transition: opacity 0.3s ease;
}

.magnifier-container:hover img {
    opacity: 0.9;
}

.magnifier-container.active {
    cursor: crosshair;
}

/* 放大镜镜头 */
.magnifier-lens {
    position: absolute;
    border: 3px solid #ff6700;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(1px);
    display: none;
    pointer-events: none;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* 放大结果显示区域 */
.magnifier-result {
    position: absolute;
    border: 3px solid #ff6700;
    border-radius: 8px;
    background-color: white;
    background-repeat: no-repeat;
    display: none;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    pointer-events: none;
}

/* 不同尺寸的放大镜 */
.magnifier-small .magnifier-lens {
    width: 80px;
    height: 80px;
}

.magnifier-small .magnifier-result {
    width: 200px;
    height: 200px;
}

.magnifier-medium .magnifier-lens {
    width: 120px;
    height: 120px;
}

.magnifier-medium .magnifier-result {
    width: 250px;
    height: 250px;
}

.magnifier-large .magnifier-lens {
    width: 150px;
    height: 150px;
}

.magnifier-large .magnifier-result {
    width: 300px;
    height: 300px;
}

/* 产品卡片中的放大镜 */
.product-card .magnifier-container {
    width: 100%;
    height: 100%;
}

.product-card .magnifier-lens {
    width: 60px;
    height: 60px;
}

.product-card .magnifier-result {
    width: 180px;
    height: 180px;
    right: -190px;
    top: 0;
}

/* 轮播图中的放大镜 */
.slider .magnifier-container {
    width: 100%;
    height: 100%;
}

.slider .magnifier-lens {
    width: 100px;
    height: 100px;
}

.slider .magnifier-result {
    width: 300px;
    height: 300px;
    right: -320px;
    top: 50%;
    transform: translateY(-50%);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .magnifier-result {
        display: none !important;
    }
    
    .magnifier-lens {
        display: none !important;
    }
    
    .magnifier-container {
        cursor: default !important;
    }
}

/* 放大镜激活时的视觉反馈 */
.magnifier-container.magnifying::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(255, 103, 0, 0.1) 50%, transparent 51%);
    pointer-events: none;
    z-index: 5;
}

/* 放大镜提示文字 */
.magnifier-hint {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 15;
}

.magnifier-container:hover .magnifier-hint {
    opacity: 1;
}

/* 加载状态 */
.magnifier-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ff6700;
    border-radius: 50%;
    animation: magnifier-spin 1s linear infinite;
    z-index: 20;
}

@keyframes magnifier-spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
