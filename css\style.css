/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

a {
    text-decoration: none;
    color: #333;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.btn {
    display: inline-block;
    padding: 8px 20px;
    background-color: #ff6700;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #ff4500;
}

/* 头部样式 */
.header {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.logo h1 {
    font-size: 24px;
    color: #ff6700;
}

.main-nav ul {
    display: flex;
}

.main-nav li {
    position: relative;
    margin: 0 15px;
}

.main-nav li a {
    padding: 10px 0;
    font-weight: bold;
}

.main-nav li a:hover {
    color: #ff6700;
}

.sub-nav {
    position: absolute;
    top: 100%;
    left: 0;
    width: 150px;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 10;
}

.main-nav li:hover .sub-nav {
    display: block;
}

.sub-nav li {
    margin: 0;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.sub-nav li:last-child {
    border-bottom: none;
}

.user-actions a {
    margin-left: 15px;
    padding: 8px 15px;
    border-radius: 4px;
}

.login-btn, .register-btn {
    border: 1px solid #ff6700;
    color: #ff6700;
    background-color: transparent;
}

.login-btn:hover, .register-btn:hover {
    background-color: #ff6700;
    color: white;
}

.login-btn.active, .register-btn.active {
    background-color: #ff6700;
    color: white;
}

.cart-btn {
    background-color: #ff6700;
    color: white;
}

/* 轮播图样式 */
.slider {
    margin-bottom: 30px;
}

.slider-container {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.slider-wrapper {
    display: flex;
    height: 100%;
    transition: transform 0.5s ease;
}

.slide {
    min-width: 100%;
    height: 100%;
    position: relative;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide-caption {
    position: absolute;
    top: 50%;
    left: 10%;
    transform: translateY(-50%);
    color: white;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.slide-caption h2 {
    font-size: 36px;
    margin-bottom: 15px;
}

.slide-caption p {
    font-size: 18px;
    margin-bottom: 20px;
}

.slide-caption .btn {
    background-color: white;
    color: #ff6700;
    font-weight: bold;
}

.slide-caption .btn:hover {
    background-color: #f5f5f5;
}

.slider-controls {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
}

.slider-controls button {
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    width: 40px;
    height: 60px;
    font-size: 20px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.3s;
}

.slider-controls button:hover {
    background-color: rgba(255, 255, 255, 0.8);
}

.slider-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    margin: 0 5px;
    cursor: pointer;
}

.dot.active {
    background-color: white;
}

/* 商品展示样式 */
.section-title {
    text-align: center;
    margin-bottom: 30px;
    font-size: 28px;
    color: #333;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.product-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.product-image img {
    width: 110%;
    height: 120%;
    object-fit: contain;
    transition: transform 0.5s;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-actions {
    position: absolute;
    bottom: -50px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 10px;
    padding: 10px;
    background-color: rgba(255, 103, 0, 0.9);
    transition: bottom 0.3s;
}

.product-card:hover .product-actions {
    bottom: 0;
}

.quick-view, .add-to-cart {
    padding: 5px 10px;
    background-color: white;
    color: #ff6700;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.quick-view:hover, .add-to-cart:hover {
    background-color: #f5f5f5;
}

.product-info {
    padding: 15px;
}

.product-info h3 {
    margin-bottom: 10px;
    font-size: 16px;
}

.product-info h3 a:hover {
    color: #ff6700;
}

.price {
    color: #ff6700;
    font-weight: bold;
    font-size: 18px;
}

.desc {
    color: #666;
    font-size: 14px;
    margin-top: 5px;
}

/* 底部样式 */
.footer {
    background-color: #333;
    color: #ccc;
    padding: 40px 0 20px;
    margin-top: 50px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    color: white;
    margin-bottom: 15px;
    font-size: 18px;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a:hover {
    color: white;
}

.copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #444;
    color: #999;
}