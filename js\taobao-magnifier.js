/**
 * 淘宝风格放大镜组件
 * 特点：方形镜头、右侧独立放大区域、高清晰度、平滑过渡
 */
class TaobaoMagnifier {
    constructor(element, options = {}) {
        this.element = element;
        this.options = {
            // 镜头尺寸 (方形)
            lensWidth: options.lensWidth || 200,
            lensHeight: options.lensHeight || 200,

            // 放大区域尺寸
            resultWidth: options.resultWidth || 400,
            resultHeight: options.resultHeight || 400,

            // 放大倍数
            zoomFactor: options.zoomFactor || 2,

            // 放大区域位置 (right, left)
            position: options.position || 'right',

            // 放大区域与原图的间距
            gap: options.gap || 20,

            // 边框颜色
            borderColor: options.borderColor || '#ff6700',

            // 镜头背景色 (半透明)
            lensBackground: options.lensBackground || 'rgba(255, 255, 255, 0.3)',

            // 是否显示提示
            showHint: options.showHint !== false,
            hintText: options.hintText || '移动鼠标查看放大效果',

            // 动画效果
            enableAnimation: options.enableAnimation !== false,
            animationDuration: options.animationDuration || 200,

            // 高清图片URL (如果不提供则使用原图)
            highResImage: options.highResImage || null,

            ...options
        };

        this.isActive = false;
        this.init();
    }

    init() {
        if (!this.element || !this.element.querySelector('img')) {
            console.warn('TaobaoMagnifier: 未找到有效的图片元素');
            return;
        }

        this.setupContainer();
        this.createElements();
        this.bindEvents();
        this.preloadHighResImage();
    }

    setupContainer() {
        this.container = this.element;
        this.image = this.container.querySelector('img');

        // 确保容器有相对定位
        if (getComputedStyle(this.container).position === 'static') {
            this.container.style.position = 'relative';
        }

        // 添加淘宝放大镜类名
        this.container.classList.add('taobao-magnifier-container');

        // 获取图片的实际显示尺寸
        this.updateImageDimensions();
    }

    updateImageDimensions() {
        const rect = this.image.getBoundingClientRect();
        this.imageWidth = this.image.offsetWidth;
        this.imageHeight = this.image.offsetHeight;
        this.imageLeft = 0;
        this.imageTop = 0;
    }

    createElements() {
        // 创建方形镜头
        this.lens = document.createElement('div');
        this.lens.className = 'taobao-magnifier-lens';
        this.lens.style.cssText = `
            position: absolute;
            width: ${this.options.lensWidth}px;
            height: ${this.options.lensHeight}px;
            border: 2px solid ${this.options.borderColor};
            background: ${this.options.lensBackground};
            cursor: move;
            display: none;
            z-index: 10;
            pointer-events: none;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        `;

        // 创建放大结果区域
        this.result = document.createElement('div');
        this.result.className = 'taobao-magnifier-result';
        this.result.style.cssText = `
            position: absolute;
            width: ${this.options.resultWidth}px;
            height: ${this.options.resultHeight}px;
            border: 2px solid ${this.options.borderColor};
            background: white;
            display: none;
            z-index: 100;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        `;

        // 创建放大图片
        this.resultImage = document.createElement('img');
        this.resultImage.style.cssText = `
            position: absolute;
            max-width: none;
            max-height: none;
            transition: ${this.options.enableAnimation ? `all ${this.options.animationDuration}ms ease-out` : 'none'};
        `;
        this.result.appendChild(this.resultImage);

        // 设置放大区域位置
        this.setResultPosition();

        // 创建提示文字
        if (this.options.showHint) {
            this.hint = document.createElement('div');
            this.hint.className = 'taobao-magnifier-hint';
            this.hint.textContent = this.options.hintText;
            this.hint.style.cssText = `
                position: absolute;
                bottom: 10px;
                left: 10px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 15;
                pointer-events: none;
            `;
            this.container.appendChild(this.hint);
        }

        // 添加元素到容器
        this.container.appendChild(this.lens);

        // 放大区域添加到body或容器的父元素，确保不被遮挡
        if (this.options.position === 'right' || this.options.position === 'left') {
            document.body.appendChild(this.result);
        } else {
            this.container.appendChild(this.result);
        }
    }

    setResultPosition() {
        // 动态计算放大区域位置
        this.updateResultPosition();
    }

    updateResultPosition() {
        const containerRect = this.container.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

        let left, top;

        switch (this.options.position) {
            case 'right':
                left = containerRect.right + scrollLeft + this.options.gap;
                top = containerRect.top + scrollTop;
                break;
            case 'left':
                left = containerRect.left + scrollLeft - this.options.resultWidth - this.options.gap;
                top = containerRect.top + scrollTop;
                break;
            default:
                left = containerRect.right + scrollLeft + this.options.gap;
                top = containerRect.top + scrollTop;
        }

        // 确保放大区域不超出视窗
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        if (left + this.options.resultWidth > viewportWidth + scrollLeft) {
            left = containerRect.left + scrollLeft - this.options.resultWidth - this.options.gap;
        }

        if (left < scrollLeft) {
            left = scrollLeft + 10;
        }

        if (top + this.options.resultHeight > viewportHeight + scrollTop) {
            top = viewportHeight + scrollTop - this.options.resultHeight - 10;
        }

        if (top < scrollTop) {
            top = scrollTop + 10;
        }

        this.result.style.left = left + 'px';
        this.result.style.top = top + 'px';
    }

    preloadHighResImage() {
        const highResUrl = this.options.highResImage ||
                          this.image.getAttribute('data-zoom-image') ||
                          this.image.src;

        if (highResUrl) {
            const img = new Image();
            img.onload = () => {
                this.resultImage.src = highResUrl;
                this.highResLoaded = true;
            };
            img.src = highResUrl;
        }
    }

    bindEvents() {
        this.container.addEventListener('mouseenter', (e) => this.onMouseEnter(e));
        this.container.addEventListener('mousemove', (e) => this.onMouseMove(e));
        this.container.addEventListener('mouseleave', (e) => this.onMouseLeave(e));

        // 监听窗口滚动和大小变化
        window.addEventListener('scroll', () => this.updateResultPosition());
        window.addEventListener('resize', () => this.updateResultPosition());
    }

    onMouseEnter(e) {
        this.isActive = true;
        this.updateImageDimensions();
        this.updateResultPosition();

        // 显示镜头和放大区域
        this.lens.style.display = 'block';
        this.result.style.display = 'block';

        // 显示提示
        if (this.hint) {
            this.hint.style.opacity = '1';
        }

        // 添加激活状态类
        this.container.classList.add('taobao-magnifier-active');

        // 设置放大图片尺寸
        this.updateResultImageSize();
    }

    onMouseMove(e) {
        if (!this.isActive) return;

        const rect = this.container.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // 计算镜头位置 (中心跟随鼠标)
        let lensX = x - this.options.lensWidth / 2;
        let lensY = y - this.options.lensHeight / 2;

        // 限制镜头在图片范围内
        lensX = Math.max(0, Math.min(lensX, this.imageWidth - this.options.lensWidth));
        lensY = Math.max(0, Math.min(lensY, this.imageHeight - this.options.lensHeight));

        // 设置镜头位置
        this.lens.style.left = lensX + 'px';
        this.lens.style.top = lensY + 'px';

        // 计算放大图片的位置
        this.updateResultImagePosition(lensX, lensY);
    }

    onMouseLeave(e) {
        this.isActive = false;

        // 隐藏镜头和放大区域
        this.lens.style.display = 'none';
        this.result.style.display = 'none';

        // 隐藏提示
        if (this.hint) {
            this.hint.style.opacity = '0';
        }

        // 移除激活状态类
        this.container.classList.remove('taobao-magnifier-active');
    }

    updateResultImageSize() {
        if (!this.resultImage.src) return;

        // 计算放大后的图片尺寸
        const scaledWidth = this.imageWidth * this.options.zoomFactor;
        const scaledHeight = this.imageHeight * this.options.zoomFactor;

        this.resultImage.style.width = scaledWidth + 'px';
        this.resultImage.style.height = scaledHeight + 'px';
    }

    updateResultImagePosition(lensX, lensY) {
        if (!this.resultImage.src) return;

        // 计算镜头在原图中的比例位置
        const ratioX = lensX / (this.imageWidth - this.options.lensWidth);
        const ratioY = lensY / (this.imageHeight - this.options.lensHeight);

        // 计算放大图片的偏移
        const scaledWidth = this.imageWidth * this.options.zoomFactor;
        const scaledHeight = this.imageHeight * this.options.zoomFactor;

        const maxOffsetX = scaledWidth - this.options.resultWidth;
        const maxOffsetY = scaledHeight - this.options.resultHeight;

        const offsetX = -ratioX * maxOffsetX;
        const offsetY = -ratioY * maxOffsetY;

        // 应用位置
        this.resultImage.style.left = offsetX + 'px';
        this.resultImage.style.top = offsetY + 'px';
    }

    // 更新图片源
    updateImage(newSrc, highResSrc = null) {
        this.image.src = newSrc;
        this.options.highResImage = highResSrc || newSrc;
        this.preloadHighResImage();
    }

    // 销毁实例
    destroy() {
        if (this.lens) this.lens.remove();
        if (this.result) this.result.remove();
        if (this.hint) this.hint.remove();

        this.container.classList.remove('taobao-magnifier-container', 'taobao-magnifier-active');

        // 移除事件监听
        window.removeEventListener('scroll', this.updateResultPosition);
        window.removeEventListener('resize', this.updateResultPosition);
    }

    // 重新计算位置 (当容器位置改变时调用)
    refresh() {
        this.updateImageDimensions();
        this.updateResultPosition();
        this.updateResultImageSize();
    }
}

// 自动初始化函数
function initTaobaoMagnifiers() {
    // 为商品详情页添加淘宝风格放大镜
    document.querySelectorAll('.product-detail .main-image').forEach(element => {
        new TaobaoMagnifier(element, {
            lensWidth: 200,
            lensHeight: 200,
            resultWidth: 400,
            resultHeight: 400,
            zoomFactor: 2.5,
            position: 'right',
            gap: 30
        });
    });

    // 为产品卡片添加小尺寸淘宝风格放大镜
    document.querySelectorAll('.product-card .product-image').forEach(element => {
        new TaobaoMagnifier(element, {
            lensWidth: 100,
            lensHeight: 100,
            resultWidth: 300,
            resultHeight: 300,
            zoomFactor: 2,
            position: 'right',
            gap: 20
        });
    });
}

// 导出类和初始化函数
window.TaobaoMagnifier = TaobaoMagnifier;
window.initTaobaoMagnifiers = initTaobaoMagnifiers;
