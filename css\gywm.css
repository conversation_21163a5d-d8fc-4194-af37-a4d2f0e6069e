/* 关于我们页面样式 */
.about-section {
    padding: 60px 0;
    background-color: #f9f9f9;
}

.about-header {
    text-align: center;
    margin-bottom: 50px;
}

.about-header h2 {
    font-size: 36px;
    color: #ff6700;
    margin-bottom: 15px;
}

.about-header .subtitle {
    font-size: 18px;
    color: #666;
}

.about-content {
    max-width: 1200px;
    margin: 0 auto;
}

.about-story, 
.about-mission, 
.about-team, 
.about-values, 
.about-contact {
    margin-bottom: 50px;
    padding: 30px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
}

.about-content h3 {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ff6700;
    display: flex;
    align-items: center;
}

.about-content h3 i {
    margin-right: 10px;
    width: 30px;
    height: 30px;
    display: inline-block;
    background-size: contain;
}

.icon-story {
    background: url('../img/icon-story.png') no-repeat center;
}

.icon-mission {
    background: url('../img/icon-mission.png') no-repeat center;
}

.icon-team {
    background: url('../img/icon-team.png') no-repeat center;
}

.icon-values {
    background: url('../img/icon-values.png') no-repeat center;
}

.about-story p {
    margin-bottom: 15px;
    line-height: 1.8;
    color: #555;
}

.about-mission ul {
    list-style-type: none;
}

.about-mission li {
    padding: 10px 0;
    padding-left: 30px;
    position: relative;
    color: #555;
}

.about-mission li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 15px;
    width: 15px;
    height: 15px;
    background-color: #ff6700;
    border-radius: 50%;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.team-member {
    text-align: center;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    transition: transform 0.3s, box-shadow 0.3s;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.team-member img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 15px;
    border: 3px solid #ff6700;
}

.team-member h4 {
    font-size: 18px;
    margin-bottom: 5px;
    color: #333;
}

.team-member .position {
    color: #ff6700;
    font-weight: bold;
    margin-bottom: 10px;
}

.team-member .bio {
    color: #666;
    font-size: 14px;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.value-item {
    text-align: center;
    padding: 20px;
}

.value-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.quality {
    background-image: url('../img/icon-quality.png');
}

.innovation {
    background-image: url('../img/icon-innovation.png');
}

.customer {
    background-image: url('../img/icon-customer.png');
}

.integrity {
    background-image: url('../img/icon-integrity.png');
}

.value-item h4 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #333;
}

.value-item p {
    color: #666;
    font-size: 14px;
}

.about-contact {
    margin-top: 50px;
}

.contact-info {
    margin-bottom: 30px;
}

.contact-info p {
    margin-bottom: 15px;
    padding-left: 30px;
    position: relative;
    color: #555;
}

.icon-address, 
.icon-phone, 
.icon-email, 
.icon-time {
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    background-size: contain;
}

.icon-address {
    background-image: url('../img/icon-address.png');
}

.icon-phone {
    background-image: url('../img/icon-phone.png');
}

.icon-email {
    background-image: url('../img/icon-email.png');
}

.icon-time {
    background-image: url('../img/icon-time.png');
}

.contact-map {
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.contact-map iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .about-header h2 {
        font-size: 28px;
    }
    
    .about-content h3 {
        font-size: 20px;
    }
    
    .team-grid, .values-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-map {
        height: 300px;
    }
}