/* 购物车页样式 */
.cart-section {
    padding: 30px 0;
    min-height: calc(100vh - 180px);
}

.cart-header {
    margin-bottom: 30px;
}

.cart-header h2 {
    font-size: 24px;
    margin-bottom: 20px;
}

.cart-steps {
    display: flex;
    justify-content: space-between;
    max-width: 800px;
    margin: 0 auto;
}

.step {
    flex: 1;
    text-align: center;
    position: relative;
}

.step:not(:last-child):after {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    width: 100%;
    height: 2px;
    background-color: #ddd;
    z-index: 1;
}

.step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    background-color: #ddd;
    color: #666;
    margin-bottom: 5px;
    position: relative;
    z-index: 2;
}

.step-text {
    font-size: 14px;
    color: #666;
}

.step.active .step-number {
    background-color: #ff6700;
    color: white;
}

.step.active .step-text {
    color: #ff6700;
    font-weight: bold;
}

.cart-table {
    margin-bottom: 30px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.cart-table table {
    width: 100%;
    border-collapse: collapse;
}

.cart-table th {
    padding: 15px;
    background-color: #f5f5f5;
    text-align: left;
    font-weight: normal;
}

.cart-table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.select-all {
    width: 120px;
}

.select-all label {
    margin-left: 5px;
    cursor: pointer;
}

.product-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.product-image {
    width: 80px;
    height: 80px;
    border: 1px solid #eee;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.product-detail h3 {
    font-size: 16px;
    margin-bottom: 5px;
}

.product-detail h3 a:hover {
    color: #ff6700;
}

.spec {
    font-size: 12px;
    color: #666;
}

.price .current-price {
    color: #ff6700;
    font-weight: bold;
}

.price .original-price {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
}

.quantity-control {
    display: flex;
    align-items: center;
}

.quantity-control button {
    width: 25px;
    height: 25px;
    border: 1px solid #ddd;
    background-color: white;
    cursor: pointer;
}

.quantity-control button:hover {
    background-color: #f5f5f5;
}

.quantity-input {
    width: 40px;
    height: 25px;
    text-align: center;
    border: 1px solid #ddd;
    border-left: none;
    border-right: none;
}

.subtotal-price {
    color: #ff6700;
    font-weight: bold;
}

.delete-btn, .favorite-btn {
    padding: 5px 10px;
    font-size: 12px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.delete-btn {
    color: #f56c6c;
    border-color: #f56c6c;
    margin-bottom: 5px;
}

.delete-btn:hover {
    background-color: #fef0f0;
}

.favorite-btn:hover {
    background-color: #f5f5f5;
}

.cart-summary {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.summary-content {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 30px;
}

.selected-info {
    font-size: 14px;
    color: #666;
}

.selected-info span {
    color: #ff6700;
    font-weight: bold;
}

.price-info {
    text-align: right;
}

.price-info p {
    margin-bottom: 5px;
    font-size: 16px;
}

#total-price {
    font-size: 24px;
    color: #ff6700;
    font-weight: bold;
}

.saving-info {
    color: #67c23a;
    font-size: 14px;
}

.checkout-btn .btn {
    padding: 12px 40px;
    font-size: 16px;
    background-color: #ff6700;
    color: white;
}

.recommend-section h3 {
    font-size: 18px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.recommend-products {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}