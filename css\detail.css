/* 详情页样式 */
.product-detail {
    padding: 30px 0;
    min-height: calc(100vh - 180px);
}

.breadcrumb {
    padding: 15px 0;
    font-size: 14px;
    color: #666;
}

.breadcrumb a {
    color: #666;
}

.breadcrumb a:hover {
    color: #ff6700;
}

.detail-container {
    display: flex;
    gap: 30px;
    margin: 30px 0;
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.product-gallery {
    width: 50%;
}

.main-image {
    position: relative;
    height: 400px;
    overflow: hidden;
    margin-bottom: 15px;
    border: 1px solid #eee;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.thumbnail-list {
    display: flex;
    gap: 10px;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border: 1px solid #ddd;
    cursor: pointer;
    overflow: hidden;
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail.active {
    border-color: #ff6700;
}

.product-info {
    width: 50%;
}

.product-title {
    font-size: 24px;
    margin-bottom: 15px;
    color: #333;
}

.product-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    font-size: 14px;
    color: #666;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.stars {
    color: #ffb400;
}

.price-section {
    background-color: #f9f9f9;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.price-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.current-price {
    font-size: 24px;
    color: #ff6700;
    font-weight: bold;
}

.original-price {
    font-size: 16px;
    color: #999;
    text-decoration: line-through;
    margin: 5px 0;
}

.discount {
    font-size: 14px;
    color: #666;
}

.promotion-section {
    margin-bottom: 20px;
}

.promotion-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.promotion-list {
    list-style: none;
}

.promotion-list li {
    padding: 5px 0;
    color: #ff6700;
    font-size: 14px;
}

.spec-item {
    margin-bottom: 15px;
}

.spec-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

.spec-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.spec-option {
    padding: 5px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
}

.spec-option.active {
    border-color: #ff6700;
    color: #ff6700;
    background-color: #fff8f3;
}

.quantity-control {
    display: flex;
    align-items: center;
    gap: 5px;
}

.quantity-control button {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background-color: white;
    cursor: pointer;
    font-size: 16px;
}

.quantity-control button:hover {
    background-color: #f5f5f5;
}

.quantity-input {
    width: 50px;
    height: 30px;
    text-align: center;
    border: 1px solid #ddd;
}

.stock-info {
    font-size: 14px;
    color: #666;
    margin-left: 15px;
}

.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.action-buttons .btn {
    padding: 12px 25px;
    font-size: 16px;
}

.add-to-cart-btn {
    background-color: #ff6700;
    color: white;
}

.buy-now-btn {
    background-color: #f56c6c;
    color: white;
}

.favorite-btn {
    background-color: white;
    color: #666;
    border: 1px solid #ddd;
}

.favorite-btn:hover {
    background-color: #f5f5f5;
}

.product-tabs {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.tab-nav {
    display: flex;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.tab-nav li {
    padding: 10px 20px;
    cursor: pointer;
    font-weight: bold;
}

.tab-nav li.active {
    color: #ff6700;
    border-bottom: 2px solid #ff6700;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane h3 {
    margin: 20px 0 15px;
    font-size: 18px;
}

.tab-pane ul {
    margin-left: 20px;
    list-style: disc;
}

.tab-pane table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.tab-pane th, .tab-pane td {
    border: 1px solid #ddd;
    padding: 10px;
    text-align: left;
}

.tab-pane th {
    background-color: #f5f5f5;
}

.detail-images {
    margin-top: 30px;
}

.detail-images img {
    max-width: 100%;
    margin-bottom: 15px;
    border: 1px solid #eee;
}