document.addEventListener('DOMContentLoaded', function() {
    // 购物车数据
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // 全选/取消全选
    const selectAll = document.getElementById('select-all');
    const itemChecks = document.querySelectorAll('.item-check');
    
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            const isChecked = this.checked;
            itemChecks.forEach(check => {
                check.checked = isChecked;
            });
            updateCartSummary();
        });
    }
    
    // 单项选择
    itemChecks.forEach(check => {
        check.addEventListener('change', function() {
            updateSelectAllState();
            updateCartSummary();
        });
    });
    
    // 更新全选状态
    function updateSelectAllState() {
        if (itemChecks.length === 0) {
            selectAll.checked = false;
            return;
        }
        
        const allChecked = Array.from(itemChecks).every(check => check.checked);
        selectAll.checked = allChecked;
    }
    
    // 数量增减
    document.querySelectorAll('.quantity-decrease').forEach(btn => {
        btn.addEventListener('click', function() {
            const input = this.nextElementSibling;
            let value = parseInt(input.value);
            if (value > 1) {
                input.value = value - 1;
                updateCartItem(this);
            }
        });
    });
    
    document.querySelectorAll('.quantity-increase').forEach(btn => {
        btn.addEventListener('click', function() {
            const input = this.previousElementSibling;
            let value = parseInt(input.value);
            if (value < 99) {
                input.value = value + 1;
                updateCartItem(this);
            }
        });
    });
    
    // 数量输入框变化
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            let value = parseInt(this.value);
            if (isNaN(value)) {
                this.value = 1;
            } else if (value < 1) {
                this.value = 1;
            } else if (value > 99) {
                this.value = 99;
            }
            updateCartItem(this);
        });
    });
    
    // 更新购物车项
    function updateCartItem(element) {
        const cartItem = element.closest('.cart-item');
        const productId = cartItem.querySelector('.item-check').getAttribute('data-id');
        const quantity = parseInt(cartItem.querySelector('.quantity-input').value);
        
        // 更新购物车数据
        const itemIndex = cart.findIndex(item => item.id === productId);
        if (itemIndex !== -1) {
            cart[itemIndex].quantity = quantity;
            localStorage.setItem('cart', JSON.stringify(cart));
        }
        
        // 更新小计
        updateSubtotal(cartItem);
        updateCartSummary();
    }
    
    // 更新小计
    function updateSubtotal(cartItem) {
        const price = parseFloat(cartItem.querySelector('.current-price').textContent.replace('¥', ''));
        const quantity = parseInt(cartItem.querySelector('.quantity-input').value);
        const subtotal = (price * quantity).toFixed(1);
        
        cartItem.querySelector('.subtotal-price').textContent = `¥${subtotal}`;
    }
    
    // 删除商品
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const cartItem = this.closest('.cart-item');
            const productId = cartItem.querySelector('.item-check').getAttribute('data-id');
            
            // 从购物车数据中移除
            cart = cart.filter(item => item.id !== productId);
            localStorage.setItem('cart', JSON.stringify(cart));
            
            // 从DOM中移除
            cartItem.remove();
            
            // 更新购物车摘要
            updateCartSummary();
            updateSelectAllState();
            
            // 更新全局购物车数量
            updateGlobalCartCount();
        });
    });
    
    // 移入收藏
    document.querySelectorAll('.favorite-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            alert('商品已移入收藏夹');
        });
    });
    
    // 更新购物车摘要
    function updateCartSummary() {
        const selectedItems = Array.from(itemChecks).filter(check => check.checked);
        const selectedCount = selectedItems.length;
        
        document.getElementById('selected-count').textContent = selectedCount;
        
        // 计算总价
        let totalPrice = 0;
        selectedItems.forEach(check => {
            const cartItem = check.closest('.cart-item');
            const price = parseFloat(cartItem.querySelector('.current-price').textContent.replace('¥', ''));
            const quantity = parseInt(cartItem.querySelector('.quantity-input').value);
            totalPrice += price * quantity;
        });
        
        document.getElementById('total-price').textContent = `¥${totalPrice.toFixed(1)}`;
    }
    
    // 更新全局购物车数量
    function updateGlobalCartCount() {
        const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
        
        // 更新当前页面的购物车数量
        document.getElementById('cart-count').textContent = totalItems;
        
        // 更新其他页面的购物车数量需要通过事件或其他方式，这里简化处理
    }
    
    // 去结算
    const checkoutBtn = document.querySelector('.checkout');
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', function() {
            const selectedItems = Array.from(itemChecks).filter(check => check.checked);
            if (selectedItems.length === 0) {
                alert('请选择要结算的商品');
                return;
            }
            
            // 实际项目中这里会跳转到结算页面
            alert('跳转到结算页面');
        });
    }
    
    // 初始化购物车摘要
    updateCartSummary();
    
    // 快速查看和加入购物车按钮事件 (与首页相同)
    document.querySelectorAll('.quick-view').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');
            window.location.href = `detail.html?id=${productId}`;
        });
    });
    
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');
            addToCart(productId);
        });
    });
    
    function addToCart(productId) {
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        const existingItem = cart.find(item => item.id === productId);
        
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            cart.push({ id: productId, quantity: 1 });
        }
        
        localStorage.setItem('cart', JSON.stringify(cart));
        updateGlobalCartCount();
        alert('商品已添加到购物车');
    }
});