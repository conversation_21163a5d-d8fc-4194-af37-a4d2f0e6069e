/* 登录页样式 */
.login-section {
    padding: 60px 0;
    min-height: calc(100vh - 180px);
    display: flex;
    align-items: center;
}

.login-form-container {
    max-width: 500px;
    margin: 0 auto;
    background-color: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.login-form-container h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

.form-group input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.form-group input:focus {
    border-color: #ff6700;
    outline: none;
}

.error-message {
    color: #ff4500;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.form-actions .btn {
    width: 48%;
    padding: 12px;
    font-size: 16px;
}

.login-btn {
    background-color: #ff6700;
    color: white;
}

.reset-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reset-btn:hover {
    background-color: #e5e5e5;
}

.form-footer {
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
}

.form-footer a {
    color: #ff6700;
}

.form-footer a:hover {
    text-decoration: underline;
}

.form-footer p {
    margin-top: 10px;
}