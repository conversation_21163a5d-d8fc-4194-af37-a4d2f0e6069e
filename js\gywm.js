document.addEventListener('DOMContentLoaded', function() {
    // 更新购物车数量
    updateCartCount();
    
    // 团队成员的悬停效果
    const teamMembers = document.querySelectorAll('.team-member');
    teamMembers.forEach(member => {
        member.addEventListener('mouseenter', function() {
            this.querySelector('img').style.transform = 'scale(1.05)';
        });
        
        member.addEventListener('mouseleave', function() {
            this.querySelector('img').style.transform = 'scale(1)';
        });
    });
    
    // 价值观项目的动画效果
    const valueItems = document.querySelectorAll('.value-item');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.1 });
    
    valueItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        observer.observe(item);
    });
    
    // 联系信息的点击效果
    const contactItems = document.querySelectorAll('.contact-info p');
    contactItems.forEach(item => {
        item.addEventListener('click', function() {
            this.style.backgroundColor = '#fff8f0';
            setTimeout(() => {
                this.style.backgroundColor = 'transparent';
            }, 300);
        });
    });
});

// 更新购物车数量
function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
    document.getElementById('cart-count').textContent = totalItems;
}