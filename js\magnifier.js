/**
 * 通用放大镜组件
 * 支持多种尺寸和配置选项
 */
class Magnifier {
    constructor(element, options = {}) {
        this.element = element;
        this.options = {
            zoomFactor: options.zoomFactor || 3,
            lensSize: options.lensSize || 120,
            resultSize: options.resultSize || 250,
            borderColor: options.borderColor || '#ff6700',
            showHint: options.showHint !== false,
            hintText: options.hintText || '悬停放大',
            position: options.position || 'right', // right, left, top, bottom
            ...options
        };
        
        this.init();
    }
    
    init() {
        if (!this.element || !this.element.querySelector('img')) {
            return;
        }
        
        this.setupContainer();
        this.createElements();
        this.bindEvents();
    }
    
    setupContainer() {
        this.container = this.element;
        this.image = this.container.querySelector('img');
        
        // 添加必要的类名
        this.container.classList.add('magnifier-container');
        
        // 设置尺寸类
        if (this.options.lensSize <= 80) {
            this.container.classList.add('magnifier-small');
        } else if (this.options.lensSize <= 120) {
            this.container.classList.add('magnifier-medium');
        } else {
            this.container.classList.add('magnifier-large');
        }
    }
    
    createElements() {
        // 创建放大镜镜头
        this.lens = document.createElement('div');
        this.lens.className = 'magnifier-lens';
        this.lens.style.width = this.options.lensSize + 'px';
        this.lens.style.height = this.options.lensSize + 'px';
        this.lens.style.borderColor = this.options.borderColor;
        
        // 创建放大结果显示区域
        this.result = document.createElement('div');
        this.result.className = 'magnifier-result';
        this.result.style.width = this.options.resultSize + 'px';
        this.result.style.height = this.options.resultSize + 'px';
        this.result.style.borderColor = this.options.borderColor;
        
        // 设置结果区域位置
        this.setResultPosition();
        
        // 创建提示文字
        if (this.options.showHint) {
            this.hint = document.createElement('div');
            this.hint.className = 'magnifier-hint';
            this.hint.textContent = this.options.hintText;
            this.container.appendChild(this.hint);
        }
        
        // 添加到容器
        this.container.appendChild(this.lens);
        this.container.appendChild(this.result);
    }
    
    setResultPosition() {
        const position = this.options.position;
        const offset = 20;
        
        switch (position) {
            case 'right':
                this.result.style.left = '100%';
                this.result.style.marginLeft = offset + 'px';
                this.result.style.top = '0';
                break;
            case 'left':
                this.result.style.right = '100%';
                this.result.style.marginRight = offset + 'px';
                this.result.style.top = '0';
                break;
            case 'top':
                this.result.style.bottom = '100%';
                this.result.style.marginBottom = offset + 'px';
                this.result.style.left = '0';
                break;
            case 'bottom':
                this.result.style.top = '100%';
                this.result.style.marginTop = offset + 'px';
                this.result.style.left = '0';
                break;
        }
    }
    
    bindEvents() {
        this.container.addEventListener('mouseenter', (e) => this.onMouseEnter(e));
        this.container.addEventListener('mousemove', (e) => this.onMouseMove(e));
        this.container.addEventListener('mouseleave', (e) => this.onMouseLeave(e));
    }
    
    onMouseEnter(e) {
        this.container.classList.add('active', 'magnifying');
        this.lens.style.display = 'block';
        this.result.style.display = 'block';
        
        // 设置放大图片
        const zoomImageUrl = this.image.getAttribute('data-zoom-image') || this.image.src;
        this.result.style.backgroundImage = `url('${zoomImageUrl}')`;
    }
    
    onMouseMove(e) {
        const rect = this.container.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const containerWidth = this.container.offsetWidth;
        const containerHeight = this.container.offsetHeight;
        
        // 计算镜头位置
        let lensX = x - this.options.lensSize / 2;
        let lensY = y - this.options.lensSize / 2;
        
        // 限制镜头在容器内
        lensX = Math.max(0, Math.min(lensX, containerWidth - this.options.lensSize));
        lensY = Math.max(0, Math.min(lensY, containerHeight - this.options.lensSize));
        
        // 设置镜头位置
        this.lens.style.left = lensX + 'px';
        this.lens.style.top = lensY + 'px';
        
        // 计算背景位置
        const bgX = -(x / containerWidth) * (containerWidth * this.options.zoomFactor - this.options.resultSize);
        const bgY = -(y / containerHeight) * (containerHeight * this.options.zoomFactor - this.options.resultSize);
        
        // 设置放大结果
        this.result.style.backgroundSize = `${containerWidth * this.options.zoomFactor}px ${containerHeight * this.options.zoomFactor}px`;
        this.result.style.backgroundPosition = `${bgX}px ${bgY}px`;
    }
    
    onMouseLeave(e) {
        this.container.classList.remove('active', 'magnifying');
        this.lens.style.display = 'none';
        this.result.style.display = 'none';
    }
    
    // 更新图片
    updateImage(newSrc, zoomSrc = null) {
        this.image.src = newSrc;
        this.image.setAttribute('data-zoom-image', zoomSrc || newSrc);
    }
    
    // 销毁实例
    destroy() {
        if (this.lens) this.lens.remove();
        if (this.result) this.result.remove();
        if (this.hint) this.hint.remove();
        
        this.container.classList.remove('magnifier-container', 'magnifier-small', 'magnifier-medium', 'magnifier-large', 'active', 'magnifying');
    }
}

// 自动初始化函数
function initMagnifiers() {
    // 为产品卡片添加放大镜
    document.querySelectorAll('.product-card .product-image').forEach(element => {
        new Magnifier(element, {
            lensSize: 60,
            resultSize: 180,
            zoomFactor: 2.5,
            position: 'right'
        });
    });
    
    // 为轮播图添加放大镜
    document.querySelectorAll('.slide').forEach(element => {
        new Magnifier(element, {
            lensSize: 100,
            resultSize: 300,
            zoomFactor: 2,
            position: 'right'
        });
    });
}

// 导出类和初始化函数
window.Magnifier = Magnifier;
window.initMagnifiers = initMagnifiers;

// DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', initMagnifiers);
