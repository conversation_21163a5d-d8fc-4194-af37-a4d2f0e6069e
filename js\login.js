document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            let isValid = true;
            
            // 验证用户名
            const usernameError = document.getElementById('username-error');
            if (!/^[a-zA-Z]+$/.test(username)) {
                usernameError.textContent = '用户名只能包含字母';
                usernameError.style.display = 'block';
                document.getElementById('username').focus();
                isValid = false;
            } else {
                usernameError.style.display = 'none';
            }
            
            // 验证密码
            const passwordError = document.getElementById('password-error');
            if (!/^[a-zA-Z0-9_]+$/.test(password)) {
                passwordError.textContent = '密码只能包含字母、数字和下划线';
                passwordError.style.display = 'block';
                
                // 如果用户名验证通过，聚焦到密码输入框
                if (isValid) {
                    document.getElementById('password').focus();
                }
                
                isValid = false;
            } else {
                passwordError.style.display = 'none';
            }
            
            // 如果验证通过，提交表单
            if (isValid) {
                alert('登录成功！');
                // 实际项目中这里应该是AJAX请求
                window.location.href = 'index.html';
            }
        });
        
        // 重置按钮
        const resetBtn = loginForm.querySelector('.reset-btn');
        resetBtn.addEventListener('click', function() {
            document.querySelectorAll('.error-message').forEach(el => {
                el.style.display = 'none';
            });
        });
    }
});